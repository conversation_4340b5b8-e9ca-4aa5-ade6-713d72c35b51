import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/components/reusable_filter_widget.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/general_controller.dart';
import 'package:gls_self_order/general/controllers/receipt_controller.dart';
import 'package:gls_self_order/general/views/finance/receipt_create_or_update_view.dart';
import 'package:gls_self_order/general/views/order/widgets/order_row_info.dart';
import 'package:intl/intl.dart';

class ReceiptView extends StatefulWidget {
  final String type;
  const ReceiptView({super.key, required this.type});

  @override
  State<ReceiptView> createState() => _ReceiptViewState();
}

class _ReceiptViewState extends State<ReceiptView> {
  //variable
  GeneralController generalController = Get.find();
  ReceiptController receiptController = Get.find();
  List receipts = [];
  List paymentMethods = [];
  List items = [];
  List groups = [];
  TextEditingController searchController = TextEditingController();
  TextEditingController dateFromController = TextEditingController();
  TextEditingController dateToController = TextEditingController();
  TextEditingController paymentMethodController = TextEditingController();
  TextEditingController itemController = TextEditingController();
  TextEditingController groupController = TextEditingController();
  int paymentMethodSelected = 0;
  int itemSelected = 0;
  int groupSelected = 0;
  DateTime today = DateTime.now();
  DateTime dateFrom = DateTime(DateTime.now().year, DateTime.now().month, 1);
  DateTime dateTo = DateTime(DateTime.now().year, DateTime.now().month + 1, 1)
      .subtract(Duration(days: 1));

  //function
  @override
  void initState() {
    super.initState();
    getData();
  }

  getData() async {
    AppFunction.showLoading();
    receipts = await receiptController.getList(
        searchController.text,
        null,
        null,
        null,
        null,
        DateFormat('yyyy-MM-dd').format(dateFrom),
        DateFormat('yyyy-MM-dd').format(dateTo),
        widget.type);
    setState(() {});
    getGroupList();
    getItemList();
    getPaymentMethodList();
    AppFunction.hideLoading();
  }

  getPaymentMethodList() async {
    paymentMethods = await generalController.getPaymentMethodList();
  }

  getGroupList() async {
    groups = await generalController.getGroupList(widget.type);
  }

  getItemList() async {
    items = await generalController.getItemList(widget.type);
  }

  Widget renderFilter() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(5),
      child: Row(
        children: [
          Expanded(
            child: CustomTextField(
              controller: searchController,
              showLabel: false,
              hint: 'Tìm kiếm số phiếu',
              space: false,
            ),
          ),
          InkWell(
            onTap: () {
              filter(false);
            },
            child: Container(
              width: 45,
              height: 45,
              margin: EdgeInsets.fromLTRB(5, 0, 0, 0),
              decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(10)),
              child: Icon(
                Icons.search,
                color: Colors.white,
              ),
            ),
          ),
          InkWell(
            onTap: () {
              showFilter(context);
            },
            child: Container(
              width: 45,
              height: 45,
              margin: EdgeInsets.fromLTRB(5, 0, 0, 0),
              decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(10)),
              child: Icon(
                Icons.filter_alt_sharp,
                color: Colors.white,
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget renderItem(item) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
              color: Colors.grey.withValues(alpha: 0.05),
              spreadRadius: 0,
              blurRadius: 1,
              offset: Offset(0, 3)),
        ],
      ),
      padding: EdgeInsets.all(10),
      margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
      child: Column(
        children: [
          OrderRowInfo(
            label: 'Số phiếu: ',
            value: item['ReceiptCode'],
          ),
          SizedBox(
            height: 10,
          ),
          OrderRowInfo(
            label: 'Chi nhánh:',
            value: item['BranchName'],
          ),
          SizedBox(
            height: 10,
          ),
          OrderRowInfo(
              label: widget.type == 'THU' ? 'Ngày thu:' : 'Ngày chi',
              value: AppFunction.formatDateWithTime(item['ReceiptDate'])),
          SizedBox(
            height: 10,
          ),
          OrderRowInfo(
              label: 'Số tiền:',
              value: AppFunction.formatMoney(item['Amount'])),
          SizedBox(
            height: 10,
          ),
          OrderRowInfo(
            label: 'Khoản mục:',
            value: item['ReceiptItemName'],
          ),
          SizedBox(
            height: 10,
          ),
          OrderRowInfo(
            label: 'Diễn giải:',
            value: item['Note'] ?? '',
            maxLines: 5,
            expandable: true,
          ),
        ],
      ),
    );
  }

  Future<void> selectDate(BuildContext context, String when) async {
    final DateTime? date = await showDatePicker(
      context: context,
      initialDate: today,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (date != null) {
      if (when == 'from') {
        dateFrom = date;
        dateFromController.text = DateFormat('dd/MM/yyyy').format(dateFrom);
      } else {
        dateTo = date;
        dateToController.text = DateFormat('dd/MM/yyyy').format(dateTo);
      }
    }
  }

  showFilter(context) {
    dateFromController.text = DateFormat('dd/MM/yyyy').format(dateFrom);
    dateToController.text = DateFormat('dd/MM/yyyy').format(dateTo);
    showGeneralDialog(
        context: context,
        pageBuilder: (context, animation, secondaryAnimation) {
          return Scaffold(
            appBar: AppBar(
              title: CustomTitleAppBar(title: 'Bộ lọc'),
              backgroundColor: AppColors.primary,
              centerTitle: true,
              automaticallyImplyLeading: false,
            ),
            body: SafeArea(
              child: Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10)),
                padding: EdgeInsets.all(5),
                child: Column(
                  children: [
                    Expanded(
                      child: ListView(
                        padding: EdgeInsets.zero,
                        children: [
                          // Row(
                          //   mainAxisAlignment: MainAxisAlignment.start,
                          //   crossAxisAlignment: CrossAxisAlignment.end,
                          //   children: [
                          //     Expanded(
                          //       child: CustomTextField(
                          //         controller: TextEditingController(text: ''),
                          //         label: 'Người nộp',
                          //         readOnly: true,
                          //       ),
                          //     ),
                          //     // InkWell(
                          //     //   onTap: () {
                          //     //
                          //     //   },
                          //     //   child: Container(
                          //     //     width: 30,
                          //     //     height: 30,
                          //     //     margin: EdgeInsets.fromLTRB(5, 0, 0, 15),
                          //     //     decoration: BoxDecoration(
                          //     //         color: AppColors.danger,
                          //     //         borderRadius: BorderRadius.circular(10)
                          //     //     ),
                          //     //     child: Icon(Icons.close, color: Colors.white,),
                          //     //   ),
                          //     // ),
                          //     InkWell(
                          //       onTap: () {
                          //
                          //       },
                          //       child: Container(
                          //         width: 30,
                          //         height: 30,
                          //         margin: EdgeInsets.fromLTRB(5, 0, 0, 15),
                          //         decoration: BoxDecoration(
                          //             color: AppColors.primary,
                          //             borderRadius: BorderRadius.circular(10)
                          //         ),
                          //         child: Icon(Icons.search, color: Colors.white,),
                          //       ),
                          //     )
                          //   ],
                          // ),
                          // Row(
                          //   mainAxisAlignment: MainAxisAlignment.start,
                          //   crossAxisAlignment: CrossAxisAlignment.end,
                          //   children: [
                          //     Expanded(
                          //       child: CustomTextField(
                          //         controller: TextEditingController(text: ''),
                          //         label: 'Người nhận',
                          //         readOnly: true,
                          //       ),
                          //     ),
                          //     // InkWell(
                          //     //   onTap: () {
                          //     //
                          //     //   },
                          //     //   child: Container(
                          //     //     width: 30,
                          //     //     height: 30,
                          //     //     margin: EdgeInsets.fromLTRB(5, 0, 0, 15),
                          //     //     decoration: BoxDecoration(
                          //     //         color: AppColors.danger,
                          //     //         borderRadius: BorderRadius.circular(10)
                          //     //     ),
                          //     //     child: Icon(Icons.close, color: Colors.white,),
                          //     //   ),
                          //     // ),
                          //     InkWell(
                          //       onTap: () {
                          //         Get.back();
                          //       },
                          //       child: Container(
                          //         width: 30,
                          //         height: 30,
                          //         margin: EdgeInsets.fromLTRB(5, 0, 0, 15),
                          //         decoration: BoxDecoration(
                          //             color: AppColors.primary,
                          //             borderRadius: BorderRadius.circular(10)
                          //         ),
                          //         child: Icon(Icons.search, color: Colors.white,),
                          //       ),
                          //     )
                          //   ],
                          // ),
                          CustomTextField(
                            controller: paymentMethodController,
                            label: 'Phương thức:',
                            readOnly: true,
                            onTap: () {
                              pickPaymentMethod();
                            },
                          ),
                          CustomTextField(
                            controller: groupController,
                            label: 'Danh mục:',
                            readOnly: true,
                            onTap: () {
                              pickGroup();
                            },
                          ),
                          CustomTextField(
                            controller: itemController,
                            label: 'Khoản mục:',
                            readOnly: true,
                            onTap: () {
                              pickItem();
                            },
                          ),
                          CustomTextField(
                            controller: dateFromController,
                            label: 'Từ ngày:',
                            readOnly: true,
                            onTap: () {
                              selectDate(context, 'from');
                            },
                          ),
                          CustomTextField(
                            controller: dateToController,
                            label: 'Đến ngày:',
                            readOnly: true,
                            onTap: () {
                              selectDate(context, 'to');
                            },
                          ),
                          // CustomTextField(
                          //   controller: TextEditingController(text: ''),
                          //   label: 'Chi nhánh:',
                          //   readOnly: true,
                          //   onTap: () {
                          //
                          //   },
                          // ),
                        ],
                      ),
                    ),
                    Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: Padding(
                            padding: EdgeInsets.fromLTRB(0, 0, 5, 0),
                            child: CustomButton(
                                text: 'Bỏ lọc',
                                onTap: () {
                                  clearFilter();
                                },
                                color: AppColors.shadow),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Padding(
                            padding: EdgeInsets.fromLTRB(5, 0, 0, 0),
                            child: CustomButton(
                                text: 'Áp dụng',
                                onTap: () {
                                  filter(true);
                                },
                                color: AppColors.primary),
                          ),
                        )
                      ],
                    )
                  ],
                ),
              ),
            ),
          );
        });
  }

  pickGroup() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        builder: (context) {
          return SizedBox(
            width: double.infinity,
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                for (dynamic item in groups)
                  InkWell(
                    onTap: () {
                      groupSelected = item['ReceiptGroupId'];
                      groupController.text = item['ReceiptGroupName'];
                      Get.back();
                    },
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1,
                                  color:
                                      AppColors.shadow.withValues(alpha: 0.1))),
                          color: item['ReceiptGroupId'] == itemSelected
                              ? AppColors.primary.withValues(alpha: 0.1)
                              : Colors.white),
                      padding: EdgeInsets.all(15),
                      child: CustomText(text: item['ReceiptGroupName']),
                    ),
                  )
              ],
            ),
          );
        });
  }

  pickItem() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        builder: (context) {
          return SizedBox(
            width: double.infinity,
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                for (dynamic item in items)
                  InkWell(
                    onTap: () {
                      itemSelected = item['ReceiptItemId'];
                      itemController.text = item['ReceiptItemName'];
                      Get.back();
                    },
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1,
                                  color:
                                      AppColors.shadow.withValues(alpha: 0.1))),
                          color: item['ReceiptItemId'] == itemSelected
                              ? AppColors.primary.withValues(alpha: 0.1)
                              : Colors.white),
                      padding: EdgeInsets.all(15),
                      child: CustomText(text: item['ReceiptItemName']),
                    ),
                  )
              ],
            ),
          );
        });
  }

  pickPaymentMethod() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        builder: (context) {
          return SizedBox(
            width: double.infinity,
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                for (dynamic item in paymentMethods)
                  InkWell(
                    onTap: () {
                      paymentMethodSelected = item['PayId'];
                      paymentMethodController.text = item['PayName'];
                      Get.back();
                    },
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1,
                                  color:
                                      AppColors.shadow.withValues(alpha: 0.1))),
                          color: item['PayId'] == paymentMethodSelected
                              ? AppColors.primary.withValues(alpha: 0.1)
                              : Colors.white),
                      padding: EdgeInsets.all(15),
                      child: CustomText(text: item['PayName']),
                    ),
                  )
              ],
            ),
          );
        });
  }

  filter(bool back) async {
    AppFunction.showLoading();
    if (back) Get.back();
    receipts = await receiptController.getList(
        searchController.text,
        groupSelected,
        itemSelected,
        paymentMethodSelected,
        null,
        DateFormat('yyyy-MM-dd').format(dateFrom),
        DateFormat('yyyy-MM-dd').format(dateTo),
        widget.type);
    AppFunction.hideLoading();
    setState(() {});
  }

  clearFilter() async {
    AppFunction.showLoading();
    searchController.text = '';
    itemSelected = 0;
    itemController.text = '';
    groupSelected = 0;
    groupController.text = '';
    paymentMethodSelected = 0;
    paymentMethodController.text = '';
    dateFrom = DateTime(DateTime.now().year, DateTime.now().month, 1);
    dateTo = DateTime(DateTime.now().year, DateTime.now().month + 1, 1)
        .subtract(Duration(days: 1));
    dateFromController.text = DateFormat('dd/MM/yyyy').format(dateFrom);
    dateToController.text = DateFormat('dd/MM/yyyy').format(dateTo);
    Get.back();
    receipts = await receiptController.getList(
        searchController.text,
        null,
        null,
        null,
        null,
        DateFormat('yyyy-MM-dd').format(dateFrom),
        DateFormat('yyyy-MM-dd').format(dateTo),
        widget.type);
    AppFunction.hideLoading();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(
            title: widget.type == 'THU' ? 'Phiếu thu' : 'Phiếu chi'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          InkWell(
            onTap: () async {
              final result = await Get.to(() => ReceiptCreateOrUpdateView(
                    type: widget.type,
                    item: null,
                  ));
              if (result != null) {
                filter(false);
              }
            },
            child: const Padding(
              padding: EdgeInsets.all(10),
              child: Icon(
                Icons.add,
                color: Colors.white,
                size: 35,
              ),
            ),
          )
        ],
      ),
      body: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          children: [
            //for filter
            renderFilter(),


            Expanded(
              child: ListView(
                padding: EdgeInsets.all(10),
                children: [
                  for (dynamic item in receipts)
                    InkWell(
                      onTap: () {
                        showDialog(
                            context: context,
                            builder: (context) {
                              return AlertDialog(
                                backgroundColor: Colors.transparent,
                                content: SizedBox(
                                  width: double.infinity,
                                  height: 150,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      CustomButton(
                                        text: 'Chỉnh sửa',
                                        onTap: () async {
                                          Get.back();
                                          final result = await Get.to(
                                              () => ReceiptCreateOrUpdateView(
                                                    type: widget.type,
                                                    item: item,
                                                  ));
                                          if (result != null) {
                                            filter(false);
                                          }
                                        },
                                        color: Colors.white,
                                        textColor: AppColors.primary,
                                        borderColor: AppColors.primary,
                                        fontSize: 18,
                                      ),
                                      SizedBox(
                                        height: 10,
                                      ),
                                      CustomButton(
                                        text: 'Xoá',
                                        onTap: () {
                                          Get.back();
                                          showDialog(
                                              context: context,
                                              builder: (context) {
                                                return AlertDialog(
                                                  title: CustomText(
                                                    text:
                                                        'Bạn có chắc muốn xoá phiếu thu này?',
                                                    maxLines: 2,
                                                  ),
                                                  actions: [
                                                    Container(
                                                      width: 100,
                                                      margin:
                                                          EdgeInsets.fromLTRB(
                                                              0, 0, 5, 0),
                                                      child: CustomButton(
                                                        text: 'cancel'.tr,
                                                        color: AppColors.shadow,
                                                        onTap: () {
                                                          Get.back();
                                                        },
                                                      ),
                                                    ),
                                                    Container(
                                                      width: 100,
                                                      margin:
                                                          EdgeInsets.fromLTRB(
                                                              0, 0, 0, 0),
                                                      child: CustomButton(
                                                        text: 'confirm'.tr,
                                                        color:
                                                            AppColors.primary,
                                                        onTap: () async {
                                                          bool success =
                                                              await receiptController
                                                                  .postDelete(
                                                                      item[
                                                                          'Id']);
                                                          if (success) {
                                                            Get.back();
                                                            getData();
                                                          }
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                );
                                              });
                                        },
                                        color: AppColors.danger,
                                        fontSize: 18,
                                      )
                                    ],
                                  ),
                                ),
                              );
                            });
                      },
                      child: renderItem(item),
                    )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
