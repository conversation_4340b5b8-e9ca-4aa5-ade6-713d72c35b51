import 'package:flutter/material.dart';
import 'package:gls_self_order/core/components/custom_text.dart';

class OrderRowInfo extends StatefulWidget {
  final String label;
  final String value;
  final bool bold;
  final Color? color;
  final int? maxLines;
  final bool expandable;

  const OrderRowInfo({
    super.key,
    required this.label,
    required this.value,
    this.bold = true,
    this.color,
    this.maxLines = 1,
    this.expandable = false,
  });

  @override
  State<OrderRowInfo> createState() => _OrderRowInfoState();
}

class _OrderRowInfoState extends State<OrderRowInfo> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    if (!widget.expandable) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 4,
            child: Align(
              alignment: Alignment.centerLeft,
              child: CustomText(
                text: widget.label,
                bold: true,
              ),
            ),
          ),
          Expanded(
            flex: 6,
            child: Align(
              alignment: Alignment.centerRight,
              child: CustomText(
                text: widget.value,
                bold: widget.bold,
                color: widget.color ?? Colors.black,
                maxLines: widget.maxLines,
                textAlign: TextAlign.right,
              ),
            ),
          ),
          
        ],
      );
    }

    // Expandable behavior
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 4,
              child: Align(
                alignment: Alignment.centerLeft,
                child: CustomText(
                  text: widget.label,
                  bold: true,
                ),
              ),
            ),
            Expanded(
              flex: 6,
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _isExpanded = !_isExpanded;
                  });
                },
                child: Align(
                  alignment: Alignment.centerRight,
                  child: CustomText(
                    text: _isExpanded
                        ? widget.value
                        : (widget.value.length > 20
                            ? '${widget.value.substring(0, 20)}...'
                            : widget.value),
                    bold: widget.bold,
                    color: widget.color ?? Colors.black,
                    maxLines: 5,
                    textAlign: TextAlign.right,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
