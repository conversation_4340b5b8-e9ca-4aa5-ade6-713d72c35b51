import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';

import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/components/reusable_filter_widget.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/general_controller.dart';
import 'package:gls_self_order/general/controllers/receipt_controller.dart';
import 'package:gls_self_order/general/views/finance/receipt_create_or_update_view.dart';
import 'package:gls_self_order/general/views/order/widgets/order_row_info.dart';
import 'package:intl/intl.dart';

class ReceiptView extends StatefulWidget {
  final String type;
  const ReceiptView({super.key, required this.type});

  @override
  State<ReceiptView> createState() => _ReceiptViewState();
}

class _ReceiptViewState extends State<ReceiptView> {
  //variable
  GeneralController generalController = Get.find();
  ReceiptController receiptController = Get.find();
  List receipts = [];
  List paymentMethods = [];
  List items = [];
  List groups = [];

  // Filter data for ReusableFilterWidget
  late FilterData filterData;

  // Keep existing filter variables for backend compatibility
  int paymentMethodSelected = 0;
  int itemSelected = 0;
  int groupSelected = 0;

  //function
  @override
  void initState() {
    super.initState();
    // Initialize FilterData
    DateTime now = DateTime.now();
    DateTime dateFrom = DateTime(now.year, now.month, 1);
    DateTime dateTo = DateTime(now.year, now.month + 1, 1).subtract(Duration(days: 1));

    filterData = FilterData(
      dateFrom: dateFrom,
      dateTo: dateTo,
      statusSelected: '',
      statusName: '',
      paymentMethodSelected: '',
      paymentMethodName: '',
      viewType: 'receipt', // Custom view type for receipt
    );

    getData();
  }

  getData() async {
    AppFunction.showLoading();
    receipts = await receiptController.getList(
        '', // No search text anymore
        groupSelected == 0 ? null : groupSelected,
        itemSelected == 0 ? null : itemSelected,
        paymentMethodSelected == 0 ? null : paymentMethodSelected,
        null,
        DateFormat('yyyy-MM-dd').format(filterData.dateFrom),
        DateFormat('yyyy-MM-dd').format(filterData.dateTo),
        widget.type);
    setState(() {});
    getGroupList();
    getItemList();
    getPaymentMethodList();
    AppFunction.hideLoading();
  }

  getPaymentMethodList() async {
    paymentMethods = await generalController.getPaymentMethodList();
  }

  getGroupList() async {
    groups = await generalController.getGroupList(widget.type);
  }

  getItemList() async {
    items = await generalController.getItemList(widget.type);
  }

  // Create custom filter data for receipt
  Map<String, String> get receiptStatuses => {
    '': 'Tất cả',
    'ACTIVE': 'Hoạt động',
    'INACTIVE': 'Không hoạt động',
  };

  void onFilterChanged(FilterData newFilterData) {
    setState(() {
      filterData = newFilterData;
    });
    getData(); // Refresh data when filter changes
  }

  void onClearFilter() {
    DateTime now = DateTime.now();
    DateTime dateFrom = DateTime(now.year, now.month, 1);
    DateTime dateTo = DateTime(now.year, now.month + 1, 1).subtract(Duration(days: 1));

    setState(() {
      filterData = FilterData(
        dateFrom: dateFrom,
        dateTo: dateTo,
        statusSelected: '',
        statusName: '',
        paymentMethodSelected: '',
        paymentMethodName: '',
        viewType: 'receipt',
      );
      // Reset backend filter variables
      paymentMethodSelected = 0;
      itemSelected = 0;
      groupSelected = 0;
    });
    getData();
  }

  Widget renderItem(item) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
              color: Colors.grey.withValues(alpha: 0.05),
              spreadRadius: 0,
              blurRadius: 1,
              offset: Offset(0, 3)),
        ],
      ),
      padding: EdgeInsets.all(10),
      margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
      child: Column(
        children: [
          OrderRowInfo(
            label: 'Số phiếu: ',
            value: item['ReceiptCode'],
          ),
          SizedBox(
            height: 10,
          ),
          OrderRowInfo(
            label: 'Chi nhánh:',
            value: item['BranchName'],
          ),
          SizedBox(
            height: 10,
          ),
          OrderRowInfo(
              label: widget.type == 'THU' ? 'Ngày thu:' : 'Ngày chi',
              value: AppFunction.formatDateWithTime(item['ReceiptDate'])),
          SizedBox(
            height: 10,
          ),
          OrderRowInfo(
              label: 'Số tiền:',
              value: AppFunction.formatMoney(item['Amount'])),
          SizedBox(
            height: 10,
          ),
          OrderRowInfo(
            label: 'Khoản mục:',
            value: item['ReceiptItemName'],
          ),
          SizedBox(
            height: 10,
          ),
          OrderRowInfo(
            label: 'Diễn giải:',
            value: item['Note'] ?? '',
            maxLines: 5,
            expandable: true,
          ),
        ],
      ),
    );
  }

  // Custom filter modal for receipt-specific filters (Group, Item, Payment Method)
  void showReceiptFilter(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Handle bar
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            SizedBox(height: 20),

            // Title
            CustomText(
              text: 'Bộ lọc chi tiết',
              size: 18,
              bold: true,
            ),
            SizedBox(height: 20),

            Expanded(
              child: ListView(
                children: [
                  // Payment Method Filter
                  _buildFilterOption(
                    'Phương thức thanh toán',
                    paymentMethods.isNotEmpty && paymentMethodSelected > 0
                        ? paymentMethods.firstWhere((item) => item['PayId'] == paymentMethodSelected)['PayName']
                        : 'Tất cả',
                    () => _showPaymentMethodPicker(),
                  ),

                  // Group Filter
                  _buildFilterOption(
                    'Danh mục',
                    groups.isNotEmpty && groupSelected > 0
                        ? groups.firstWhere((item) => item['ReceiptGroupId'] == groupSelected)['ReceiptGroupName']
                        : 'Tất cả',
                    () => _showGroupPicker(),
                  ),

                  // Item Filter
                  _buildFilterOption(
                    'Khoản mục',
                    items.isNotEmpty && itemSelected > 0
                        ? items.firstWhere((item) => item['ReceiptItemId'] == itemSelected)['ReceiptItemName']
                        : 'Tất cả',
                    () => _showItemPicker(),
                  ),
                ],
              ),
            ),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(right: 8),
                    child: CustomButton(
                      text: 'Bỏ lọc',
                      onTap: () {
                        setState(() {
                          paymentMethodSelected = 0;
                          itemSelected = 0;
                          groupSelected = 0;
                        });
                        Navigator.pop(context);
                        getData();
                      },
                      color: AppColors.shadow,
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(left: 8),
                    child: CustomButton(
                      text: 'Áp dụng',
                      onTap: () {
                        Navigator.pop(context);
                        getData();
                      },
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterOption(String title, String value, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        margin: EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.shadow.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(text: title, size: 14, color: Colors.grey.shade600),
                SizedBox(height: 4),
                CustomText(text: value, size: 16),
              ],
            ),
            Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
          ],
        ),
      ),
    );
  }

  void _showGroupPicker() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) {
          return Container(
            height: MediaQuery.of(context).size.height * 0.5,
            padding: EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Handle bar
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                SizedBox(height: 20),

                CustomText(text: 'Chọn danh mục', size: 18, bold: true),
                SizedBox(height: 20),

                // All option
                InkWell(
                  onTap: () {
                    setState(() {
                      groupSelected = 0;
                    });
                    Get.back();
                  },
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      border: Border(bottom: BorderSide(width: 1, color: AppColors.shadow.withValues(alpha: 0.1))),
                      color: groupSelected == 0 ? AppColors.primary.withValues(alpha: 0.1) : Colors.white,
                    ),
                    child: CustomText(text: 'Tất cả'),
                  ),
                ),

                Expanded(
                  child: ListView(
                    children: [
                      for (dynamic item in groups)
                        InkWell(
                          onTap: () {
                            setState(() {
                              groupSelected = item['ReceiptGroupId'];
                            });
                            Get.back();
                          },
                          child: Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                                border: Border(
                                    bottom: BorderSide(
                                        width: 1,
                                        color: AppColors.shadow.withValues(alpha: 0.1))),
                                color: item['ReceiptGroupId'] == groupSelected
                                    ? AppColors.primary.withValues(alpha: 0.1)
                                    : Colors.white),
                            padding: EdgeInsets.all(15),
                            child: CustomText(text: item['ReceiptGroupName']),
                          ),
                        )
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }

  void _showItemPicker() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) {
          return Container(
            height: MediaQuery.of(context).size.height * 0.5,
            padding: EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Handle bar
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                SizedBox(height: 20),

                CustomText(text: 'Chọn khoản mục', size: 18, bold: true),
                SizedBox(height: 20),

                // All option
                InkWell(
                  onTap: () {
                    setState(() {
                      itemSelected = 0;
                    });
                    Get.back();
                  },
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      border: Border(bottom: BorderSide(width: 1, color: AppColors.shadow.withValues(alpha: 0.1))),
                      color: itemSelected == 0 ? AppColors.primary.withValues(alpha: 0.1) : Colors.white,
                    ),
                    child: CustomText(text: 'Tất cả'),
                  ),
                ),

                Expanded(
                  child: ListView(
                    children: [
                      for (dynamic item in items)
                        InkWell(
                          onTap: () {
                            setState(() {
                              itemSelected = item['ReceiptItemId'];
                            });
                            Get.back();
                          },
                          child: Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                                border: Border(
                                    bottom: BorderSide(
                                        width: 1,
                                        color: AppColors.shadow.withValues(alpha: 0.1))),
                                color: item['ReceiptItemId'] == itemSelected
                                    ? AppColors.primary.withValues(alpha: 0.1)
                                    : Colors.white),
                            padding: EdgeInsets.all(15),
                            child: CustomText(text: item['ReceiptItemName']),
                          ),
                        )
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }

  void _showPaymentMethodPicker() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) {
          return Container(
            height: MediaQuery.of(context).size.height * 0.5,
            padding: EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Handle bar
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                SizedBox(height: 20),

                CustomText(text: 'Chọn phương thức thanh toán', size: 18, bold: true),
                SizedBox(height: 20),

                // All option
                InkWell(
                  onTap: () {
                    setState(() {
                      paymentMethodSelected = 0;
                    });
                    Get.back();
                  },
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      border: Border(bottom: BorderSide(width: 1, color: AppColors.shadow.withValues(alpha: 0.1))),
                      color: paymentMethodSelected == 0 ? AppColors.primary.withValues(alpha: 0.1) : Colors.white,
                    ),
                    child: CustomText(text: 'Tất cả'),
                  ),
                ),

                Expanded(
                  child: ListView(
                    children: [
                      for (dynamic item in paymentMethods)
                        InkWell(
                          onTap: () {
                            setState(() {
                              paymentMethodSelected = item['PayId'];
                            });
                            Get.back();
                          },
                          child: Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                                border: Border(
                                    bottom: BorderSide(
                                        width: 1,
                                        color: AppColors.shadow.withValues(alpha: 0.1))),
                                color: item['PayId'] == paymentMethodSelected
                                    ? AppColors.primary.withValues(alpha: 0.1)
                                    : Colors.white),
                            padding: EdgeInsets.all(15),
                            child: CustomText(text: item['PayName']),
                          ),
                        )
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }

  // Get filter summary text
  String _getFilterSummary() {
    List<String> activeFilters = [];

    if (paymentMethodSelected > 0 && paymentMethods.isNotEmpty) {
      try {
        var method = paymentMethods.firstWhere((item) => item['PayId'] == paymentMethodSelected);
        activeFilters.add('PT: ${method['PayName']}');
      } catch (e) {
        // Handle case where payment method not found
      }
    }

    if (groupSelected > 0 && groups.isNotEmpty) {
      try {
        var group = groups.firstWhere((item) => item['ReceiptGroupId'] == groupSelected);
        activeFilters.add('DM: ${group['ReceiptGroupName']}');
      } catch (e) {
        // Handle case where group not found
      }
    }

    if (itemSelected > 0 && items.isNotEmpty) {
      try {
        var item = items.firstWhere((item) => item['ReceiptItemId'] == itemSelected);
        activeFilters.add('KM: ${item['ReceiptItemName']}');
      } catch (e) {
        // Handle case where item not found
      }
    }

    if (activeFilters.isEmpty) {
      return 'Chưa có bộ lọc nào được áp dụng';
    }

    return 'Đang lọc: ${activeFilters.join(', ')}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(
            title: widget.type == 'THU' ? 'Phiếu thu' : 'Phiếu chi'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          InkWell(
            onTap: () async {
              final result = await Get.to(() => ReceiptCreateOrUpdateView(
                    type: widget.type,
                    item: null,
                  ));
              if (result != null) {
                getData();
              }
            },
            child: const Padding(
              padding: EdgeInsets.all(10),
              child: Icon(
                Icons.add,
                color: Colors.white,
                size: 35,
              ),
            ),
          )
        ],
      ),
      body: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          children: [
            // Filter widget - using ReusableFilterWidget for date filter only
            ReusableFilterWidget(
              filterData: filterData,
              statuses: receiptStatuses,
              paymentMethods: paymentMethods,
              onFilterChanged: onFilterChanged,
              onClearFilter: onClearFilter,
              showOnlyDateFilter: true, // Only show date filter
            ),

            // Additional filter button for receipt-specific filters
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Expanded(
                    child: CustomText(
                      text: _getFilterSummary(),
                      size: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  InkWell(
                    onTap: () => showReceiptFilter(context),
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.filter_list, color: Colors.white, size: 16),
                          SizedBox(width: 4),
                          CustomText(text: 'Lọc thêm', color: Colors.white, size: 14),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            Expanded(
              child: ListView(
                padding: EdgeInsets.all(10),
                children: [
                  for (dynamic item in receipts)
                    InkWell(
                      onTap: () {
                        showDialog(
                            context: context,
                            builder: (context) {
                              return AlertDialog(
                                backgroundColor: Colors.transparent,
                                content: SizedBox(
                                  width: double.infinity,
                                  height: 150,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      CustomButton(
                                        text: 'Chỉnh sửa',
                                        onTap: () async {
                                          Get.back();
                                          final result = await Get.to(
                                              () => ReceiptCreateOrUpdateView(
                                                    type: widget.type,
                                                    item: item,
                                                  ));
                                          if (result != null) {
                                            getData();
                                          }
                                        },
                                        color: Colors.white,
                                        textColor: AppColors.primary,
                                        borderColor: AppColors.primary,
                                        fontSize: 18,
                                      ),
                                      SizedBox(
                                        height: 10,
                                      ),
                                      CustomButton(
                                        text: 'Xoá',
                                        onTap: () {
                                          Get.back();
                                          showDialog(
                                              context: context,
                                              builder: (context) {
                                                return AlertDialog(
                                                  title: CustomText(
                                                    text:
                                                        'Bạn có chắc muốn xoá phiếu thu này?',
                                                    maxLines: 2,
                                                  ),
                                                  actions: [
                                                    Container(
                                                      width: 100,
                                                      margin:
                                                          EdgeInsets.fromLTRB(
                                                              0, 0, 5, 0),
                                                      child: CustomButton(
                                                        text: 'cancel'.tr,
                                                        color: AppColors.shadow,
                                                        onTap: () {
                                                          Get.back();
                                                        },
                                                      ),
                                                    ),
                                                    Container(
                                                      width: 100,
                                                      margin:
                                                          EdgeInsets.fromLTRB(
                                                              0, 0, 0, 0),
                                                      child: CustomButton(
                                                        text: 'confirm'.tr,
                                                        color:
                                                            AppColors.primary,
                                                        onTap: () async {
                                                          bool success =
                                                              await receiptController
                                                                  .postDelete(
                                                                      item[
                                                                          'Id']);
                                                          if (success) {
                                                            Get.back();
                                                            getData();
                                                          }
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                );
                                              });
                                        },
                                        color: AppColors.danger,
                                        fontSize: 18,
                                      )
                                    ],
                                  ),
                                ),
                              );
                            });
                      },
                      child: renderItem(item),
                    )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
