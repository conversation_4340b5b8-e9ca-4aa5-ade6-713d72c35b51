import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';

import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/components/reusable_filter_widget.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/general_controller.dart';
import 'package:gls_self_order/general/controllers/receipt_controller.dart';
import 'package:gls_self_order/general/views/finance/receipt_create_or_update_view.dart';
import 'package:gls_self_order/general/views/order/widgets/order_row_info.dart';
import 'package:intl/intl.dart';

class ReceiptView extends StatefulWidget {
  final String type;
  const ReceiptView({super.key, required this.type});

  @override
  State<ReceiptView> createState() => _ReceiptViewState();
}

class _ReceiptViewState extends State<ReceiptView> {
  //variable
  GeneralController generalController = Get.find();
  ReceiptController receiptController = Get.find();
  List receipts = [];
  List paymentMethods = [];
  List items = [];
  List groups = [];

  // Filter data for ReusableFilterWidget
  late FilterData filterData;

  // Keep existing filter variables for backend compatibility
  int paymentMethodSelected = 0;
  int itemSelected = 0;
  int groupSelected = 0;

  //function
  @override
  void initState() {
    super.initState();
    // Initialize FilterData
    DateTime now = DateTime.now();
    DateTime dateFrom = DateTime(now.year, now.month, 1);
    DateTime dateTo =
        DateTime(now.year, now.month + 1, 1).subtract(Duration(days: 1));

    filterData = FilterData(
      dateFrom: dateFrom,
      dateTo: dateTo,
      statusSelected: '',
      statusName: '',
      paymentMethodSelected: '',
      paymentMethodName: '',
      viewType: 'receipt', // Custom view type for receipt
    );

    getData();
  }

  getData() async {
    AppFunction.showLoading();
    receipts = await receiptController.getList(
        '', // No search text anymore
        groupSelected == 0 ? null : groupSelected,
        itemSelected == 0 ? null : itemSelected,
        paymentMethodSelected == 0 ? null : paymentMethodSelected,
        null,
        DateFormat('yyyy-MM-dd').format(filterData.dateFrom),
        DateFormat('yyyy-MM-dd').format(filterData.dateTo),
        widget.type);
    setState(() {});
    getGroupList();
    getItemList();
    getPaymentMethodList();
    AppFunction.hideLoading();
  }

  getPaymentMethodList() async {
    paymentMethods = await generalController.getPaymentMethodList();
  }

  getGroupList() async {
    groups = await generalController.getGroupList(widget.type);
  }

  getItemList() async {
    items = await generalController.getItemList(widget.type);
  }

  // Create custom filter data for receipt
  Map<String, String> get receiptStatuses => {
        '': 'Tất cả',
        'ACTIVE': 'Hoạt động',
        'INACTIVE': 'Không hoạt động',
      };

  void onFilterChanged(FilterData newFilterData) {
    setState(() {
      filterData = newFilterData;
    });
    getData(); // Refresh data when filter changes
  }

  void onClearFilter() {
    DateTime now = DateTime.now();
    DateTime dateFrom = DateTime(now.year, now.month, 1);
    DateTime dateTo =
        DateTime(now.year, now.month + 1, 1).subtract(Duration(days: 1));

    setState(() {
      filterData = FilterData(
        dateFrom: dateFrom,
        dateTo: dateTo,
        statusSelected: '',
        statusName: '',
        paymentMethodSelected: '',
        paymentMethodName: '',
        viewType: 'receipt',
      );
      // Reset backend filter variables
      paymentMethodSelected = 0;
      itemSelected = 0;
      groupSelected = 0;
    });
    getData();
  }

  Widget renderItem(item) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
              color: Colors.grey.withValues(alpha: 0.05),
              spreadRadius: 0,
              blurRadius: 1,
              offset: Offset(0, 3)),
        ],
      ),
      padding: EdgeInsets.all(10),
      margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
      child: Column(
        children: [
          OrderRowInfo(
            label: 'Số phiếu: ',
            value: item['ReceiptCode'],
          ),
          SizedBox(
            height: 10,
          ),
          OrderRowInfo(
            label: 'Chi nhánh:',
            value: item['BranchName'],
          ),
          SizedBox(
            height: 10,
          ),
          OrderRowInfo(
              label: widget.type == 'THU' ? 'Ngày thu:' : 'Ngày chi',
              value: AppFunction.formatDateWithTime(item['ReceiptDate'])),
          SizedBox(
            height: 10,
          ),
          OrderRowInfo(
              label: 'Số tiền:',
              value: AppFunction.formatMoney(item['Amount'])),
          SizedBox(
            height: 10,
          ),
          OrderRowInfo(
            label: 'Khoản mục:',
            value: item['ReceiptItemName'],
          ),
          SizedBox(
            height: 10,
          ),
          OrderRowInfo(
            label: 'Diễn giải:',
            value: item['Note'] ?? '',
            maxLines: 5,
            expandable: true,
          ),
        ],
      ),
    );
  }

  void _showGroupPicker() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) {
          return Container(
            height: MediaQuery.of(context).size.height * 0.5,
            padding: EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Handle bar
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                SizedBox(height: 20),

                CustomText(text: 'Chọn danh mục', size: 18, bold: true),
                SizedBox(height: 20),

                // All option
                InkWell(
                  onTap: () {
                    setState(() {
                      groupSelected = 0;
                    });
                    Get.back();
                  },
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              width: 1,
                              color: AppColors.shadow.withValues(alpha: 0.1))),
                      color: groupSelected == 0
                          ? AppColors.primary.withValues(alpha: 0.1)
                          : Colors.white,
                    ),
                    child: CustomText(text: 'Tất cả'),
                  ),
                ),

                Expanded(
                  child: ListView(
                    children: [
                      for (dynamic item in groups)
                        InkWell(
                          onTap: () {
                            setState(() {
                              groupSelected = item['ReceiptGroupId'];
                            });
                            Get.back();
                          },
                          child: Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                                border: Border(
                                    bottom: BorderSide(
                                        width: 1,
                                        color: AppColors.shadow
                                            .withValues(alpha: 0.1))),
                                color: item['ReceiptGroupId'] == groupSelected
                                    ? AppColors.primary.withValues(alpha: 0.1)
                                    : Colors.white),
                            padding: EdgeInsets.all(15),
                            child: CustomText(text: item['ReceiptGroupName']),
                          ),
                        )
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }

  void _showItemPicker() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) {
          return Container(
            height: MediaQuery.of(context).size.height * 0.5,
            padding: EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Handle bar
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                SizedBox(height: 20),

                CustomText(text: 'Chọn khoản mục', size: 18, bold: true),
                SizedBox(height: 20),

                // All option
                InkWell(
                  onTap: () {
                    setState(() {
                      itemSelected = 0;
                    });
                    Get.back();
                  },
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              width: 1,
                              color: AppColors.shadow.withValues(alpha: 0.1))),
                      color: itemSelected == 0
                          ? AppColors.primary.withValues(alpha: 0.1)
                          : Colors.white,
                    ),
                    child: CustomText(text: 'Tất cả'),
                  ),
                ),

                Expanded(
                  child: ListView(
                    children: [
                      for (dynamic item in items)
                        InkWell(
                          onTap: () {
                            setState(() {
                              itemSelected = item['ReceiptItemId'];
                            });
                            Get.back();
                          },
                          child: Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                                border: Border(
                                    bottom: BorderSide(
                                        width: 1,
                                        color: AppColors.shadow
                                            .withValues(alpha: 0.1))),
                                color: item['ReceiptItemId'] == itemSelected
                                    ? AppColors.primary.withValues(alpha: 0.1)
                                    : Colors.white),
                            padding: EdgeInsets.all(15),
                            child: CustomText(text: item['ReceiptItemName']),
                          ),
                        )
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }

  void _showPaymentMethodPicker() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) {
          return Container(
            height: MediaQuery.of(context).size.height * 0.5,
            padding: EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Handle bar
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                SizedBox(height: 20),

                CustomText(
                    text: 'Chọn phương thức thanh toán', size: 18, bold: true),
                SizedBox(height: 20),

                // All option
                InkWell(
                  onTap: () {
                    setState(() {
                      paymentMethodSelected = 0;
                    });
                    Get.back();
                  },
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              width: 1,
                              color: AppColors.shadow.withValues(alpha: 0.1))),
                      color: paymentMethodSelected == 0
                          ? AppColors.primary.withValues(alpha: 0.1)
                          : Colors.white,
                    ),
                    child: CustomText(text: 'Tất cả'),
                  ),
                ),

                Expanded(
                  child: ListView(
                    children: [
                      for (dynamic item in paymentMethods)
                        InkWell(
                          onTap: () {
                            setState(() {
                              paymentMethodSelected = item['PayId'];
                            });
                            Get.back();
                          },
                          child: Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                                border: Border(
                                    bottom: BorderSide(
                                        width: 1,
                                        color: AppColors.shadow
                                            .withValues(alpha: 0.1))),
                                color: item['PayId'] == paymentMethodSelected
                                    ? AppColors.primary.withValues(alpha: 0.1)
                                    : Colors.white),
                            padding: EdgeInsets.all(15),
                            child: CustomText(text: item['PayName']),
                          ),
                        )
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }

  // Build filter chip widget
  Widget _buildFilterChip({
    required BuildContext context,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(24),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(24),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomText(
              text: label,
              size: 14,
              color: Colors.grey.shade700,
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.keyboard_arrow_down,
              size: 16,
              color: Colors.grey.shade600,
            ),
          ],
        ),
      ),
    );
  }

  // Get date label for filter chip
  String _getDateLabel() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    final fromDate = DateTime(filterData.dateFrom.year,
        filterData.dateFrom.month, filterData.dateFrom.day);
    final toDate = DateTime(
        filterData.dateTo.year, filterData.dateTo.month, filterData.dateTo.day);

    // Check for today
    if (fromDate == today && toDate == today) {
      return 'Hôm nay';
    }

    // Check for yesterday
    if (fromDate == yesterday && toDate == yesterday) {
      return 'Hôm qua';
    }

    // Check for current month
    final firstDayOfMonth = DateTime(now.year, now.month, 1);
    final lastDayOfMonth = DateTime(now.year, now.month + 1, 0);
    if (fromDate.year == firstDayOfMonth.year &&
        fromDate.month == firstDayOfMonth.month &&
        fromDate.day == firstDayOfMonth.day &&
        toDate.year == lastDayOfMonth.year &&
        toDate.month == lastDayOfMonth.month &&
        toDate.day == lastDayOfMonth.day) {
      return 'Tháng này';
    }

    // Default format
    if (fromDate == toDate) {
      return DateFormat('dd/MM/yyyy').format(fromDate);
    }

    return '${DateFormat('dd/MM').format(fromDate)} - ${DateFormat('dd/MM/yyyy').format(toDate)}';
  }

  // Get payment method label for filter chip
  String _getPaymentMethodLabel() {
    if (paymentMethodSelected > 0 && paymentMethods.isNotEmpty) {
      try {
        var method = paymentMethods
            .firstWhere((item) => item['PayId'] == paymentMethodSelected);
        return method['PayName'];
      } catch (e) {
        return 'Phương thức';
      }
    }
    return 'Phương thức';
  }

  // Get group label for filter chip
  String _getGroupLabel() {
    if (groupSelected > 0 && groups.isNotEmpty) {
      try {
        var group = groups
            .firstWhere((item) => item['ReceiptGroupId'] == groupSelected);
        return group['ReceiptGroupName'];
      } catch (e) {
        return 'Danh mục';
      }
    }
    return 'Danh mục';
  }

  // Get item label for filter chip
  String _getItemLabel() {
    if (itemSelected > 0 && items.isNotEmpty) {
      try {
        var item =
            items.firstWhere((item) => item['ReceiptItemId'] == itemSelected);
        return item['ReceiptItemName'];
      } catch (e) {
        return 'Khoản mục';
      }
    }
    return 'Khoản mục';
  }

  // Show date filter modal using ReusableFilterWidget
  void _showDateFilterModal(BuildContext context) {
    ReusableFilterWidget.showDateFilterModal(
      context,
      filterData,
      (newFilterData) {
        setState(() {
          filterData = newFilterData;
        });
        getData(); // Auto refresh data when filter changes
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(
            title: widget.type == 'THU' ? 'Phiếu thu' : 'Phiếu chi'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          InkWell(
            onTap: () async {
              final result = await Get.to(() => ReceiptCreateOrUpdateView(
                    type: widget.type,
                    item: null,
                  ));
              if (result != null) {
                getData();
              }
            },
            child: const Padding(
              padding: EdgeInsets.all(10),
              child: Icon(
                Icons.add,
                color: Colors.white,
                size: 35,
              ),
            ),
          )
        ],
      ),
      body: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          children: [
            // Custom filter chips for receipt
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    // Date filter chip
                    _buildFilterChip(
                      context: context,
                      label: _getDateLabel(),
                      onTap: () => _showDateFilterModal(context),
                    ),
                    const SizedBox(width: 12),

                    // Payment method filter chip
                    _buildFilterChip(
                      context: context,
                      label: _getPaymentMethodLabel(),
                      onTap: () => _showPaymentMethodPicker(),
                    ),
                    const SizedBox(width: 12),

                    // Group filter chip
                    _buildFilterChip(
                      context: context,
                      label: _getGroupLabel(),
                      onTap: () => _showGroupPicker(),
                    ),
                    const SizedBox(width: 12),

                    // Item filter chip
                    _buildFilterChip(
                      context: context,
                      label: _getItemLabel(),
                      onTap: () => _showItemPicker(),
                    ),
                  ],
                ),
              ),
            ),

            Expanded(
              child: ListView(
                padding: EdgeInsets.all(10),
                children: [
                  for (dynamic item in receipts)
                    InkWell(
                      onTap: () {
                        showDialog(
                            context: context,
                            builder: (context) {
                              return AlertDialog(
                                backgroundColor: Colors.transparent,
                                content: SizedBox(
                                  width: double.infinity,
                                  height: 150,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      CustomButton(
                                        text: 'Chỉnh sửa',
                                        onTap: () async {
                                          Get.back();
                                          final result = await Get.to(
                                              () => ReceiptCreateOrUpdateView(
                                                    type: widget.type,
                                                    item: item,
                                                  ));
                                          if (result != null) {
                                            getData();
                                          }
                                        },
                                        color: Colors.white,
                                        textColor: AppColors.primary,
                                        borderColor: AppColors.primary,
                                        fontSize: 18,
                                      ),
                                      SizedBox(
                                        height: 10,
                                      ),
                                      CustomButton(
                                        text: 'Xoá',
                                        onTap: () {
                                          Get.back();
                                          showDialog(
                                              context: context,
                                              builder: (context) {
                                                return AlertDialog(
                                                  title: CustomText(
                                                    text:
                                                        'Bạn có chắc muốn xoá phiếu thu này?',
                                                    maxLines: 2,
                                                  ),
                                                  actions: [
                                                    Container(
                                                      width: 100,
                                                      margin:
                                                          EdgeInsets.fromLTRB(
                                                              0, 0, 5, 0),
                                                      child: CustomButton(
                                                        text: 'cancel'.tr,
                                                        color: AppColors.shadow,
                                                        onTap: () {
                                                          Get.back();
                                                        },
                                                      ),
                                                    ),
                                                    Container(
                                                      width: 100,
                                                      margin:
                                                          EdgeInsets.fromLTRB(
                                                              0, 0, 0, 0),
                                                      child: CustomButton(
                                                        text: 'confirm'.tr,
                                                        color:
                                                            AppColors.primary,
                                                        onTap: () async {
                                                          bool success =
                                                              await receiptController
                                                                  .postDelete(
                                                                      item[
                                                                          'Id']);
                                                          if (success) {
                                                            Get.back();
                                                            getData();
                                                          }
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                );
                                              });
                                        },
                                        color: AppColors.danger,
                                        fontSize: 18,
                                      )
                                    ],
                                  ),
                                ),
                              );
                            });
                      },
                      child: renderItem(item),
                    )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
